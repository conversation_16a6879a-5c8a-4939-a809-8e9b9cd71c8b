import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/common/modal-common";
import imageRequest from "@/modules/image/api/image.api";
import { Button, Input, Switch } from "antd";
import { useState, useRef, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";

export default function AddOcrModal({
  stateAddOcr,
  setStateAddOcr,
  setDirectOCRdata,
  image,
  directOCRdata,
}: {
  stateAddOcr: {
    isOpen: boolean;
    x: number;
    y: number;
    rowIndex: number;
  };
  setStateAddOcr: (state: any) => void;
  setDirectOCRdata: (data: any) => void;
  image: any;
  directOCRdata: any;
}) {
  const inputRef = useRef<any>(null);

  const handleCancel = () => {
    setStateAddOcr({ isOpen: false, x: 0, y: 0 });
  };

  const [depth, setDepth] = useState<string>("");
  const [type, setType] = useState<string>("wooden");

  // Focus input when modal opens
  useEffect(() => {
    if (stateAddOcr.isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [stateAddOcr.isOpen]);

  const handleAdd = async () => {
    const data = [...directOCRdata];
    data.push({
      height: 10,
      id: uuidv4(),
      rowIndex: stateAddOcr.rowIndex,
      text: depth,
      type: type,
      width: 10,
      x: stateAddOcr.x,
      y: stateAddOcr.y,
      probability: 1,
    });
    setDirectOCRdata(data);

    const res = await imageRequest.updateResultOCR({
      id: image?.id,
      ocr: JSON.stringify(data),
    });
    setStateAddOcr({ isOpen: false, x: 0, y: 0, rowIndex: 0 });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAdd();
    }
  };

  return (
    <ModalCommon
      open={stateAddOcr.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={350}
      closable={false}
    >
      <div className="flex flex-col gap-2">
        <div className="grid grid-cols-6 gap-4">
          <div className="col-span-6 flex flex-col gap-2">
            <p className="text-sm font-medium">Depth</p>
            <Input
              ref={inputRef}
              placeholder="Enter depth"
              value={depth}
              onChange={(e) => setDepth(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button onClick={handleCancel}>Cancel</Button>
          <Button type="primary" onClick={handleAdd}>
            Add
          </Button>
        </div>
      </div>
    </ModalCommon>
  );
}
