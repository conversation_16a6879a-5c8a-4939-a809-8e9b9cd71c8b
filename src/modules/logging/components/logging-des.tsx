import { EditOutlined } from "@ant-design/icons";
import { Button, Tag } from "antd";
import { Fragment } from "react";

export interface ILoggingDescProps {
  dataEntry: any;
  setModalState: any;
  index: any;
}

export function LoggingDesc({ dataEntry, setModalState }: ILoggingDescProps) {
  return (
    <Fragment>
      <div className="flex items-center justify-between gap-1 border rounded-lg border-gray-300 w-full">
        <div className="p-1 flex flex-col gap-2">
          <div className="flex">
            <div className="flex gap-1 font-medium">
              <div>From</div>
              <Tag className="mr-0" color="blue">
                {dataEntry?.depthFrom?.toFixed(2) ?? "--"}m
              </Tag>
              <div>to</div>
              <Tag className="mr-0" color="gold">
                {dataEntry?.depthTo?.toFixed(2) ?? "--"}m
              </Tag>
            </div>
          </div>
        </div>
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => {
            setModalState({
              isOpen: true,
              detail: dataEntry,
              type: "create",
            });
          }}
        />
      </div>
    </Fragment>
  );
}
